export class IPageRequest {
  pageNum?: number;
  pageSize?: number;
}

export class IPageResponse<T> {
  pageNum?: number;
  pageSize?: number;
  total?: number;
  records?: T[];
  list?: {
    records?: T[];
  };
}

export interface Result<T = any> {
  code: string;
  message: string;
  success: boolean;
  data?: T;
}

export interface MiceResult<T> {
  code: string;
  message: string;
  success: boolean;
  data?: T;
}

export interface Editable<R> {
  save(request: R): Promise<Result>;

  edit(request: R): Promise<Result>;
}

export interface Deleteable {
  remove(id: number): Promise<Result>;
}
