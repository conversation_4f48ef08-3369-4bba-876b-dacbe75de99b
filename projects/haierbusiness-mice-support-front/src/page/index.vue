<script setup lang="ts">
import eHeader from '@haierbusiness-front/components/layout2/Header.vue';
import AIImg from '@/assets/image/home/<USER>';
import { throttle } from 'lodash';
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { resolveParam } from '@haierbusiness-front/utils';

const route = useRoute();
const theme = ref<'primary' | 'default'>('default');
const onScroll = throttle(() => {
  if (window.scrollY > 78) {
    theme.value = 'primary';
  } else {
    theme.value = 'default';
  }
}, 200);

const hideBtn = ref<string>('');

onMounted(() => {
  const record = resolveParam(route.query.record);
  hideBtn.value = record.hideBtn || '';

  if (route.path === '/index') {
    theme.value = 'default';
    window.addEventListener('scroll', onScroll);
  } else {
    theme.value = 'primary';
  }
});

onUnmounted(() => {
  window.removeEventListener('scroll', onScroll);
});

watch(route, () => {
  if (route.path === '/index') {
    theme.value = 'default';
    window.addEventListener('scroll', onScroll);
  } else {
    theme.value = 'primary';
    window.removeEventListener('scroll', onScroll);
  }
});
</script>

<template>
  <div class="home">
    <div v-if="hideBtn !== '1'">
      <e-header :theme="'primary'" />
      <div class="space" />
    </div>
    <router-view />
    <div class="right-float">
      <img :src="AIImg" class="AI" />
    </div>
  </div>
</template>

<style scoped lang="less">
.home {
  font-family: PingFangSC, PingFang SC;
  position: relative;
  height: auto;
  overflow: hidden;
  background: #f6f9fc;
  .space {
    height: 78px;
  }
  .right-float {
    position: fixed;
    /* bottom: calc(50% + 60px); */
    bottom: 330px;
    right: 0;
    transform: translate(0, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    img.AI {
      width: 99px;
    }
  }
}
</style>
