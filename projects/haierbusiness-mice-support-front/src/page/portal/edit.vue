<script lang="ts" setup>
import {
  message,
  Modal,
} from 'ant-design-vue';
import { computed, h, onMounted, reactive, ref, VNode, watch } from 'vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import type { SizeType } from 'ant-design-vue/es/config-provider';
import { useRoute, useRouter } from 'vue-router';
import { IConsultantFilter, IMeetingDetails, ServiceProviderFilter } from '@haierbusiness-front/common-libs';
import { consultantApi, meetingAttendeeApi, serviceProviderApi } from '@haierbusiness-front/apis';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import dayjs, { Dayjs } from 'dayjs';
import { storeToRefs } from 'pinia';
const currentRouter = ref()
const route = useRoute();
const router = useRouter();
const id = Number(route.query.id)
const loading = ref(false)
const { loginUser } = storeToRefs(applicationStore());
console.log(loginUser, "loginUser");
//是否是招标系统
let isEdit = false
//经办人
const Handler = ref('')

const meetingDetails = ref<IMeetingDetails>({})


interface HotelItem {
  id: number;
  hotelName: string;
  hotelAddress: string;
  resourceHotelLeadIntoId: number | null;
  platformHotelId: number | null;
}

//酒店循环
const meetingHotel = ref<HotelItem[]>([
  {
    id: 1,
    hotelName: '',
    hotelAddress: '',
    resourceHotelLeadIntoId: null,
    platformHotelId: null,
  }
])

//获取会议详情
const meetingDetail = async () => {
  const response = await meetingAttendeeApi.details(id)
  if (response) {
    meetingDetails.value = response
  }
  let Hotel = []
  console.log(meetingDetails.value, "meetingDetails.value");
  if (typeof meetingDetails.value.miceHotelName == 'string' && meetingDetails.value.miceHotelName) {
    Hotel = meetingDetails.value.miceHotelName.split(',')
    console.log(Hotel, "Hotel");
    meetingHotel.value = []
    Hotel.forEach((res: string, index: number) => {
      meetingHotel.value.push({
        id: index + 1,
        hotelName: res,
        hotelAddress: '',
        resourceHotelLeadIntoId: null,
        platformHotelId: null
      })
    });
  }
  if (meetingDetails.value.miceStartDate && meetingDetails.value.miceEndDate) {
    meetingDate.value = [
      dayjs(meetingDetails.value.miceStartDate),
      dayjs(meetingDetails.value.miceEndDate)
    ]
  }
  let consultantUserCodeList: string[] = []
  let consultantUsernameList: string[] = []
  if (meetingDetails.value.consultantUserCode && meetingDetails.value.consultantUsername) {
    consultantUserCodeList = meetingDetails.value.consultantUserCode.split(',')
    consultantUsernameList = meetingDetails.value.consultantUsername.split(',')
  }
  // console.log(consultantUserCodeList, consultantUsernameList);
  selectedCounsellors.value = consultantUserCodeList.map((item, index) => ({
    label: consultantUsernameList[index],
    value: item
  }));
  console.log(selectedCounsellors.value, "selectedCounsellors.value");

  meetingDetails.value.operatorName = loginUser.value?.nickName
  meetingDetails.value.operatorCode = loginUser.value?.username

  Handler.value = `${loginUser.value?.nickName}(${loginUser.value?.username})`

  if (meetingDetails.value.miceSource == 1) {
    isEdit = true
  }

}
// 会议时间范围
const meetingDate = ref<[Dayjs, Dayjs]>()
watch(() => meetingDate.value, (n: any) => {
  if (n) {
    meetingDetails.value.miceStartDate = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    meetingDetails.value.miceEndDate = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    meetingDetails.value.miceStartDate = undefined
    meetingDetails.value.miceEndDate = undefined
  }
});

onMounted(async () => {
  meetingDetail()
  await fetchConsultants();
})

const deleteHotel = (index: any, item: any) => {
  if (meetingHotel.value.length == 1) {
    return
  } else {
    meetingHotel.value.splice(index, 1)
  }
}
const addHotel = (key: number, item: any) => {
  meetingHotel.value.push({
    id: key + 1,
    hotelName: '',
    hotelAddress: '',
    resourceHotelLeadIntoId: null,
    platformHotelId: null
  })
}

// 酒店搜索相关
const hotelSearchVisible = ref(false);
const currentHotelIndex = ref(0);
const hotelSearchValue = ref('');
const hotelSearchResults = ref<any[]>([]);

// 打开酒店搜索弹框
const openHotelSearch = (index: number) => {
  currentHotelIndex.value = index;
  hotelSearchVisible.value = true;
  hotelSearchValue.value = '';
  loadAllHotels();
};
// 加载所有酒店数据
const loadAllHotels = () => {
  serviceProviderApi.getHotelList({} as ServiceProviderFilter)
    .then(res => {
      const records = Array.isArray(res) ? res : (res?.records || []);
      hotelSearchResults.value = records;
    })
    .catch(error => {
      message.error('加载酒店列表失败');
    });
};
// 搜索酒店
const searchHotel = () => {
  if (hotelSearchValue.value) {
    serviceProviderApi.getHotelList({
      platformHotelName: hotelSearchValue.value
    } as ServiceProviderFilter)
      .then(res => {
        const records = Array.isArray(res) ? res : (res?.records || []);
        hotelSearchResults.value = records;
      })
      .catch(error => {
        message.error('搜索酒店失败');
      });
  } else {
    loadAllHotels();
  }
};
// 选择酒店
const selectHotel = (hotel: any) => {
  const currentHotel = meetingHotel.value[currentHotelIndex.value];
  currentHotel.hotelName = hotel.platformHotelName || '';
  currentHotel.hotelAddress = hotel.platformHotelAddress || hotel.platformHotelName;
  currentHotel.resourceHotelLeadIntoId = hotel.resourceHotelLeadIntoId;
  currentHotel.platformHotelId = hotel.platformHotelCode;

  hotelSearchVisible.value = false;
};
// 关闭酒店搜索弹框
const closeHotelSearch = () => {
  hotelSearchVisible.value = false;
};

//实例
const options = [
  { label: '招标系统', value: 1 },
  { label: '会中系统', value: 2 }
]

const handleSubmit = () => {
  loading.value = true
  let hotelName: string[] = []
  meetingHotel.value.forEach((item) => {
    hotelName.push(item.hotelName)
  })
  if (hotelName.length > 0) {
    meetingDetails.value.miceHotelName = hotelName.join()
  }
  let name: string[] = []
  let code: string[] = []
  if (selectedCounsellors.value) {
    selectedCounsellors.value.forEach((item) => {
      name.push(item.label)
      code.push(item.value)
    })
  }
  meetingDetails.value.consultantUserCode = code.join(',')
  meetingDetails.value.consultantUsername = name.join(',')
  console.log(meetingDetails.value, "meetingDetails");
  try {
    meetingAttendeeApi.meetingEdit(meetingDetails.value)
      .then(() => {
        message.success('编辑会议成功')
        router.push('/support/meeting/index')
        loading.value = false
      })
      .catch(() => {
        message.error('编辑会议失败')
      })
  } catch (error) {
    loading.value = false
  } finally {
    loading.value = false
  }


}
//会议顾问
// 定义选项类型
interface UserOption {
  label: string;
  value: string;
  id?: number | null;
}
// 人员选项
const userOptions = ref<UserOption[]>([]);
//选中的顾问
const selectedCounsellors = ref<{ label: string, value: string }[]>([]);

// 查询会议顾问列表
const fetchConsultants = async (keyword = '') => {
  const params: IConsultantFilter = {
    pageNum: 1,
    pageSize: 99999,
    state: 0,
  };

  if (keyword) {
    params.keyword = keyword;
  }

  try {
    const res = await consultantApi.list(params);
    if (res && res.records) {
      // 打印第一条数据，查看结构
      if (res.records.length > 0) {
        for (const key in res.records[0]) {
        }
      }

      userOptions.value = res.records.map((item) => ({
        label: item.nickName || '未命名顾问',
        value: item.username || '',
        id: item.id, // 确保这里使用的是id
      }));

    }
  } catch (error) {
    message.error('获取会议顾问列表失败');
  }
};

// 搜索关键词
const searchKeyword = ref('');

// 处理搜索
const handleSearch = (value: string) => {
  searchKeyword.value = value;
  fetchConsultants(value);
};


// 处理顾问选择变更
const handleCounsellorChange = (value: any, options: any) => {
  console.log(value, "values");
  console.log(selectedCounsellors.value, "selectedCounsellors.value");

}

</script>
<template>
  <div class="container">
    <div class="content">
      <div class="content-top">
        会议首页/{{ router.currentRoute?.value.meta.title }}
      </div>
      <div class="main">
        <div class="main-top">
          <div class="top-left">
            {{ router.currentRoute?.value.meta.title }}
          </div>
          <div class="top-right">
            <a-button type="primary" @click="handleSubmit" :loading="loading">提交</a-button>
          </div>
        </div>
        <div class="main-middle">
          <a-row :gutter="[16, 16]" class="row-top">
            <a-col :span="4" style="text-align: right;line-height: 32px;">会议名称：</a-col>
            <a-col :span="7">
              <a-input v-model:value="meetingDetails.miceName" placeholder="请输入会议名称" allow-clear :maxlength="200" />
            </a-col>
            <a-col :span="4" style="text-align: right;line-height: 32px;">会议来源：</a-col>
            <a-col :span="7">
              <a-select v-model:value="meetingDetails.miceSource" style="width: 100%" placeholder="请选择会议来源"
                :options="options" disabled />
            </a-col>
          </a-row>
          <a-row :gutter="[16, 16]" class="row-top">
            <a-col :span="4" style="text-align: right;line-height: 32px;">会议时间：</a-col>
            <a-col :span="7">
              <a-range-picker id="meetingDate" v-model:value="meetingDate" value-format="YYYY-MM-DD" format="YYYY-MM-DD"
                style="width: 100%" allow-clear />
            </a-col>
            <a-col :span="4" style="text-align: right;line-height: 32px;">关联会议：</a-col>
            <a-col :span="7">
              {{ meetingDetails?.miceConnectMeetingId }}
            </a-col>
          </a-row>
          <a-row :gutter="[16, 16]" class="row-top" v-for="(item, index) in meetingHotel" :key="index">
            <a-col :span="4" style="text-align: right;line-height: 32px;">会议酒店：</a-col>
            <a-col :span="7">
              <a-input v-model:value="item.hotelName" placeholder="请选择酒店" readonly @click="openHotelSearch(index)"
                :disabled="isEdit">
                <template #suffix>
                  <SearchOutlined @click="openHotelSearch(index)" />
                </template>
              </a-input>
            </a-col>
            <a-col :span="3">
              <a-button type="link" @click="deleteHotel(index, item)">删除</a-button>
              <a-button type="link" v-if="index + 1 == meetingHotel.length"
                @click="addHotel(item.id, item)">添加</a-button>
            </a-col>
          </a-row>
          <a-row :gutter="[16, 16]" class="row-top">
            <a-col :span="4" style="text-align: right;line-height: 32px;">经办人：</a-col>
            <a-col :span="7">
              <a-input id="merchantName" v-model:value="Handler" placeholder="请输入" allow-clear :maxlength="200"
                disabled />
            </a-col>
            <a-col :span="4" style="text-align: right;line-height: 32px;">会议顾问：</a-col>
            <a-col :span="7">
              <a-select v-model:value="selectedCounsellors" mode="tags" label-in-value :options="userOptions" @search="handleSearch"
                style="width: 100%;" @change="handleCounsellorChange" disabled>
                <template #option="{ label, value }">
                  <span class="option-text">{{ label }}<em>(ID:{{ value }})</em></span>
                </template>
                <template #tagRender="{ label, value }">
                  <a-tag>{{ label }}({{ value }})</a-tag>
                </template>
              </a-select>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
    <!-- 添加酒店搜索弹框 -->
    <a-modal v-model:visible="hotelSearchVisible" title="搜索酒店" class="hotel-search-modal" @cancel="closeHotelSearch">
      <div class="search-input">
        <a-input v-model:value="hotelSearchValue" placeholder="请输入酒店名称搜索" allowClear :maxlength="200">
          <template #suffix>
            <SearchOutlined @click="searchHotel" />
          </template>
        </a-input>
        <div class="search-tip">首次打开显示全部酒店，可输入名称进行搜索</div>
      </div>
      <div class="hotel-search-results">
        <div v-if="hotelSearchResults.length === 0" class="empty-data">
          <p>暂无数据</p>
        </div>
        <div v-else>
          <div v-for="hotel in hotelSearchResults" :key="hotel.id" class="hotel-search-item"
            @click="selectHotel(hotel)">
            <div class="hotel-name">酒店名称：{{ hotel.platformHotelName }}</div>
            <div class="hotel-other-info" v-if="hotel.enterpriseCode">酒店编码：{{ hotel.enterpriseCode }}</div>
          </div>
        </div>
      </div>

      <template #footer>
        <a-button @click="closeHotelSearch">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<style lang="scss" scoped>
* {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
}

ul,
p {
  margin-bottom: 0;
}

ul li {
  list-style: none;
}

.container {
  width: 100%;
  background: #F6F7F9;
}

.content {
  width: 1280px;
  height: auto;
  margin: 0 auto;

  .main {
    width: 100%;
    height: 800px;
    background-color: #fff;

    .main-top {
      width: 100%;
      border: 1px solid #E5E6EB;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 18px 24px;
    }

    .row-top {
      margin-top: 40px;
    }
  }
}

.content-top {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909C;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  padding: 16px 0px;
}

.ant-btn {
  border-radius: 4px;
}

.hotel-search-modal {
  width: 650px !important;

  .search-input {
    margin-bottom: 16px;

    .search-tip {
      margin-top: 8px;
      color: #999;
      font-size: 12px;
    }
  }

  .hotel-search-results {
    margin-top: 16px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 4px;

    .empty-data {
      text-align: center;
      padding: 30px 0;
      color: #999;
    }

    .hotel-search-item {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: #f5f5f5;
      }

      .hotel-name {
        font-weight: 500;
        margin-bottom: 4px;
        font-size: 14px;
      }

      .hotel-other-info {
        color: #666;
        font-size: 12px;
      }
    }
  }
}
</style>